/**
 * CSV Page Generator Admin Styles
 *
 * @package ReasonDigital\CSVPageGenerator
 * <AUTHOR> Digital Developer
 * @license GPL-2.0-or-later
 * @link    https://github.com/reason-digital/wordpress-csv-plugin
 */

/* Variables */
:root {
    --csv-primary-color: #0073aa;
    --csv-success-color: #00a32a;
    --csv-error-color: #d63638;
    --csv-warning-color: #dba617;
    --csv-border-color: #ccd0d4;
    --csv-background-color: #f6f7f7;
    --csv-card-background: #ffffff;
    --csv-text-color: #1d2327;
    --csv-border-radius: 4px;
    --csv-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Main admin container */
.csv-page-generator-admin {
    max-width: 1200px;
    margin: 0 auto;
}

.csv-page-generator-admin .card {
    background: var(--csv-card-background);
    border: 1px solid var(--csv-border-color);
    border-radius: var(--csv-border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--csv-box-shadow);
}

.csv-page-generator-admin .card h2,
.csv-page-generator-admin .card h3 {
    margin-top: 0;
    color: var(--csv-text-color);
    font-weight: 600;
}

.csv-page-generator-admin .card h2 {
    font-size: 1.3em;
    margin-bottom: 15px;
}

.csv-page-generator-admin .card h3 {
    font-size: 1.1em;
    margin-bottom: 10px;
}

/* Upload section */
.upload-section .csv-file-upload {
    position: relative;
    border: 2px dashed var(--csv-border-color);
    border-radius: var(--csv-border-radius);
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: var(--csv-background-color);
}

.upload-section .csv-file-upload:hover,
.upload-section .csv-file-upload.drag-over {
    border-color: var(--csv-primary-color);
    background: rgba(0, 115, 170, 0.05);
}

.upload-section .csv-file-upload input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--csv-border-color);
    border-radius: var(--csv-border-radius);
    background: var(--csv-card-background);
}

.upload-section .file-upload-info {
    margin-top: 10px;
}

.upload-section .file-upload-info .file-selected {
    color: var(--csv-success-color);
    font-weight: 500;
}

.upload-section .csv-upload-actions {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-section .csv-upload-actions .spinner {
    float: none;
    margin: 0;
}

/* Progress section */
.progress-section .progress-bar-container {
    margin: 15px 0;
}

.progress-section .progress-bar {
    width: 100%;
    height: 24px;
    background-color: var(--csv-background-color);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--csv-border-color);
}

.progress-section .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--csv-success-color), #46b450);
    transition: width 0.3s ease;
    border-radius: 12px;
}

.progress-section .progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: var(--csv-text-color);
}

.progress-section .progress-text .progress-percentage {
    font-weight: 600;
}

.progress-section .progress-text .progress-status {
    font-style: italic;
}

.progress-section .progress-details {
    margin: 15px 0;
    padding: 15px;
    background: var(--csv-background-color);
    border-radius: var(--csv-border-radius);
    border: 1px solid var(--csv-border-color);
}

.progress-section .progress-details p {
    margin: 5px 0;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
}

.progress-section .progress-details .success-count {
    color: var(--csv-success-color);
}

.progress-section .progress-details .error-count {
    color: var(--csv-error-color);
}

.progress-section .progress-actions {
    margin-top: 15px;
}

/* Results section */
.results-section .results-summary {
    margin: 15px 0;
}

.results-section .results-summary .results-stats {
    padding: 15px;
    background: var(--csv-background-color);
    border-radius: var(--csv-border-radius);
    border-left: 4px solid var(--csv-success-color);
}

.results-section .results-summary .results-stats p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: var(--csv-success-color);
}

.results-section .results-summary .results-stats ul {
    margin: 0;
    padding-left: 20px;
}

.results-section .results-summary .results-stats ul li {
    margin: 5px 0;
    font-size: 14px;
}

.results-section .results-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Help section */
.help-section .help-content h4 {
    margin: 15px 0 10px 0;
    color: var(--csv-text-color);
    font-size: 1em;
}

.help-section .help-content ul {
    margin: 0 0 15px 20px;
}

.help-section .help-content ul li {
    margin: 5px 0;
    font-size: 14px;
}

.help-section .help-content ul li strong {
    color: var(--csv-primary-color);
}

.help-section .help-content pre {
    background: var(--csv-background-color);
    padding: 15px;
    border-radius: var(--csv-border-radius);
    border: 1px solid var(--csv-border-color);
    overflow-x: auto;
    font-size: 13px;
    line-height: 1.4;
}

.help-section .help-content pre code {
    background: none;
    padding: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Form elements */
.form-table th {
    font-weight: 600;
    color: var(--csv-text-color);
}

.form-table .description {
    font-size: 13px;
    color: #646970;
    margin-top: 5px;
}

/* Buttons */
.button.button-primary {
    background: var(--csv-primary-color);
    border-color: var(--csv-primary-color);
}

.button.button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.button.button-secondary:hover {
    border-color: var(--csv-primary-color);
    color: var(--csv-primary-color);
}

/* Notices */
.notice.notice-csv-success {
    border-left-color: var(--csv-success-color);
}

.notice.notice-csv-error {
    border-left-color: var(--csv-error-color);
}

.notice.notice-csv-warning {
    border-left-color: var(--csv-warning-color);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.status-success {
    background-color: var(--csv-success-color);
}

.status-indicator.status-error {
    background-color: var(--csv-error-color);
}

.status-indicator.status-warning {
    background-color: var(--csv-warning-color);
}

.status-indicator.status-processing {
    background-color: var(--csv-primary-color);
    animation: pulse 1.5s infinite;
}

/* Animation for drag and drop */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.csv-file-upload.drag-over {
    animation: pulse 1s infinite;
}

/* Responsive design */
@media (max-width: 768px) {
    .csv-page-generator-admin .card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .csv-page-generator-admin .progress-text {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .csv-page-generator-admin .results-actions,
    .csv-page-generator-admin .csv-upload-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .csv-page-generator-admin .results-actions .button,
    .csv-page-generator-admin .csv-upload-actions .button {
        margin-bottom: 5px;
    }
}

/* Import History Styles */
.wrap .csv-history-stats {
    margin: 20px 0 !important;
}

.wrap .csv-history-stats .stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.wrap .csv-history-stats .stat-card {
    background: #fff !important;
    border: 1px solid #c3c4c7 !important;
    border-radius: 4px !important;
    padding: 20px !important;
    text-align: center !important;
    box-shadow: 0 1px 1px rgba(0,0,0,.04) !important;
}

.wrap .csv-history-stats .stat-card h3 {
    font-size: 2.5em !important;
    margin: 0 0 10px 0 !important;
    color: #1d2327 !important;
    font-weight: 600 !important;
}

.wrap .csv-history-stats .stat-card p {
    margin: 0 !important;
    color: #646970 !important;
    font-weight: 500 !important;
}

.wrap .status-badge {
    padding: 4px 8px !important;
    border-radius: 3px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
}

.wrap .status-completed {
    background: #d1e7dd !important;
    color: #0f5132 !important;
}

.wrap .status-pending {
    background: #fff3cd !important;
    color: #664d03 !important;
}

.wrap .status-processing {
    background: #cff4fc !important;
    color: #055160 !important;
}

.wrap .status-failed {
    background: #f8d7da !important;
    color: #721c24 !important;
}

.wrap .success-count {
    color: #0f5132 !important;
    font-weight: 600 !important;
}

.wrap .failed-count.error {
    color: #721c24 !important;
    font-weight: 600 !important;
}

.wrap .error-log-details {
    margin-top: 10px !important;
    padding: 10px !important;
    background: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
}

.wrap .error-log-details pre {
    margin: 0 !important;
    white-space: pre-wrap !important;
    font-size: 12px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
}

.wrap .csv-history-table .column-id { width: 60px !important; }
.wrap .csv-history-table .column-status { width: 100px !important; }
.wrap .csv-history-table .column-rows { width: 80px !important; }
.wrap .csv-history-table .column-success { width: 100px !important; }
.wrap .csv-history-table .column-failed { width: 80px !important; }
.wrap .csv-history-table .column-size { width: 100px !important; }
.wrap .csv-history-table .column-date { width: 150px !important; }
.wrap .csv-history-table .column-actions { width: 150px !important; }
