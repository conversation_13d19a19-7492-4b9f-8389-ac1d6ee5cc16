# **Reason Digital**

# **Wordpress Developer**

Jul 25

Hello \- Welcome to our Technical Task\!

There is no 'right' or 'wrong' answer to this task; we use this to determine your familiarity with some of the tech we use, and how you go about architecting and writing code.

There is no time limit as such, but we'd appreciate it if you could respond **within the next three days**. If this will not be possible then please let us know when we can expect a response.

Please include a DDEV setup so we can run your code on our machines.

We would like to see your code, so please upload to Github, make access public and send us a link.

###

### **Task**

1) Create a Wordpress plugin that allows a user to upload a CSV file in the admin that contains titles and description for webpages, and after uploading, the plugin should create pages in Wordpress in draft mode with the data from the CSV
2) Using the default Wordpress theme, modify it so that the pages show the information from the CSV

### **Bonus**

1) Modify the Wordpress pages so that a JWT token is required to view them

#### **Things we are looking for**

* We are looking for how you architect the code and how you choose to write it.
* Even though this is a small project, we are looking for a good setup of toolkits.
* We are looking for clean, readable code.
* You can deploy it to a free website host if you wish.

#### **Things we are not looking for**

* Pixel perfect designs.

As stated in the introduction \- there isn’t one single ‘right’ solution that we are looking for. This task is set up to assess your skills and to give us an insight into your working process.

***Please submit code that you would be proud to share and think accurately represents your method and coding quality.***

Please upload your code to a host where we have full access.

We will chat over your solution at interview. As well as the output of the exercise we are also interested in your reflections on it \- so please don’t let perfect be the enemy of done\!

Please let us know if anything is unclear, or if you have any further questions by responding directly to the email this was attached to

All the best

The Reason Team