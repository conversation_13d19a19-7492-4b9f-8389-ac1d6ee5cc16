{"name": "reason-digital/wordpress-csv-plugin", "description": "WordPress plugin for CSV file upload and page generation", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "Reason Digital Developer", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "composer/installers": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.7", "wp-coding-standards/wpcs": "^3.0", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "phpcompatibility/php-compatibility": "^9.3", "brain/monkey": "^2.6", "mockery/mockery": "^1.5"}, "autoload": {"psr-4": {"ReasonDigital\\CSVPageGenerator\\": "wp-content/plugins/csv-page-generator/src/"}}, "autoload-dev": {"psr-4": {"ReasonDigital\\CSVPageGenerator\\Tests\\": "wp-content/plugins/csv-page-generator/tests/"}}, "scripts": {"test": "phpunit", "test:coverage": "phpunit --coverage-html coverage", "phpcs": "phpcs --standard=WordPress wp-content/plugins/csv-page-generator/src/", "phpcbf": "phpcbf --standard=WordPress wp-content/plugins/csv-page-generator/src/", "install-codestandards": ["Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin::run"]}, "config": {"allow-plugins": {"composer/installers": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}