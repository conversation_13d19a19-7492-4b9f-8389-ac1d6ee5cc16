# Wordpress - ignore core, configuration, examples, uploads and logs.
# https://github.com/github/gitignore/blob/main/WordPress.gitignore

# Core
#
# Note: if you want to stage/commit WP core files
# you can delete this whole section/until Configuration.
/wp-admin/
/wp-content/index.php
/wp-content/languages
/wp-content/plugins/index.php
/wp-content/themes/index.php
/wp-includes/
/index.php
/license.txt
/readme.html
/wp-*.php
/xmlrpc.php

# Configuration
wp-config.php

# Example themes
/wp-content/themes/twenty*/

# Example plugin
/wp-content/plugins/hello.php

# Uploads
/wp-content/uploads/

# Log files
*.log

# htaccess
/.htaccess

# All plugins
#
# Note: If you wish to whitelist plugins,
# uncomment the next line
#/wp-content/plugins

# All themes
#
# Note: If you wish to whitelist themes,
# uncomment the next line
#/wp-content/themes
.specstory/

# DDEV
.ddev/.homeadditions/
.ddev/.global_commands/
.ddev/commands/
.ddev/homeadditions/
.ddev/import-db/
.ddev/mysql/
.ddev/postgres/
.ddev/redis/
.ddev/solr/
.ddev/traefik/
.ddev/.dbimageBuild/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.pnp.*
.yarn/

# Composer
vendor/
composer.lock

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Build and dist directories
dist/
build/
coverage/
.nyc_output/

# Testing
phpunit.xml.local
tests/_output/
tests/_support/_generated/
.phpunit.result.cache

# Plugin specific
wp-content/plugins/csv-page-generator/assets/dist/
wp-content/plugins/csv-page-generator/coverage/
wp-content/plugins/csv-page-generator/vendor/
wp-content/plugins/csv-page-generator/node_modules/
wp-content/plugins/akismet/

# CSV upload files (for development)
wp-content/uploads/csv-imports/

# Backup files
*.bak
*.backup
*.sql
